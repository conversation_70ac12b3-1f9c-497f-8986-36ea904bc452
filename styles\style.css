


html {
    height: 100%;
    width: 100%;
}
body {
    margin: 0;
    min-height: 100%; /* height grows if content grows */
    min-width: 100%;
    position: absolute;

}
.container {
    max-width: 500px;
    margin: auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .title {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 24px;
    font-weight: 600;
  }
  
  .form input, .form select {
    width: 100%;
    margin: 8px 0;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 16px;
  }
  
  .toast {
    background-color: #4caf50;
    color: white;
    padding: 12px;
    margin-bottom: 16px;
    border-radius: 4px;
    text-align: center;
  }
  
  .button-group {
    display: flex;
    justify-content: center;
    gap: 10px;
  }
  
  .btn-primary, .btn-secondary {
    padding: 12px 20px;
    font-size: 16px;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
  }
  
  .btn-primary {
    background-color: #007bff;
  }
  
  .btn-secondary {
    background-color: #6c757d;
  }
  
  .btn-primary:hover {
    background-color: #0056b3;
  }
  
  .btn-secondary:hover {
    background-color: #5a6268;
  }
  