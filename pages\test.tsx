/*import { Container, Title, Stack, Text } from "@mantine/core";
import { TimeInput } from "@mantine/dates";
import { useState } from "react";

export default function TestHeure() {
  const [time, setTime] = useState<string>("");

  return (
    <Container size="sm" p="md">
      <Title order={3}>Test TimeInput</Title>
      <Stack spacing="sm">
        <TimeInput
          label="Heure"
          value={time}
          onChange={(val: string) => setTime(val)}
          placeholder="HH:mm"
        />
        <Text>Heure sélectionnée : {time || "—"}</Text>
      </Stack>
    </Container>
  );
}
*/