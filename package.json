{"scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.9.11", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mantine/core": "^6.0.22", "@mantine/dates": "^6.0.22", "@mantine/dropzone": "^6.0.22", "@mantine/form": "^6.0.0", "@mantine/hooks": "^6.0.22", "@mantine/notifications": "^6.0.22", "@mantine/tiptap": "^6.0.22", "@shadcn/ui": "^0.0.4", "@stomp/stompjs": "^6.1.2", "@tabler/icons-react": "^2.47.0", "@tiptap/extension-link": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "axios": "^1.7.9", "cors": "^2.8.5", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "deepmerge": "^4.3.0", "frappe-gantt": "^1.0.3", "graphql": "^16.8.1", "install": "^0.13.0", "jsonwebtoken": "^9.0.2", "keycloak-js": "^26.2.0", "lodash": "^4.17.21", "next": "^14.1.0", "next-auth": "^4.24.11", "next-i18next": "^15.2.0", "npm": "^10.3.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.18.0", "react-dom": "^18.2.0", "react-frappe-gantt": "^0.2.3", "react-icons": "^5.0.1", "react-router-dom": "^7.4.1", "react-toastify": "^11.0.5", "react-vertical-timeline-component": "^3.5.3", "sass": "^1.70.0", "sockjs-client": "^1.6.1", "subscriptions-transport-ws": "^0.11.0", "universal-cookie": "^7.2.2"}, "devDependencies": {"@types/keycloak-js": "^3.4.1", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-big-calendar": "^1.16.1", "autoprefixer": "^10.4.17", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "5.3.3"}}