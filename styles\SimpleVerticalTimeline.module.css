.timelineContainer {
  padding: 20px 10px;
}

.timelineItem {
  position: relative;
  padding-left: 30px;
  margin-bottom: 30px;
}

.timelineItem:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: -30px;
  width: 2px;
  background-color: #E2E8F0;
}

.timelineItem:last-child:before {
  bottom: 0;
}

.timelineBullet {
  position: absolute;
  left: -14px;
  top: 0;
  border: 3px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timelineContent {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.timelineDate {
  display: inline-block;
  margin-bottom: 10px;
  font-weight: 600;
  font-size: 14px;
  color: #4A5568;
}

.timelineTitle {
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 5px;
  color: #3E5879;
}

.timelineDescription {
  font-size: 14px;
  color: #718096;
  white-space: pre-wrap;
}

.timelineFooter {
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timelineEmpty {
  text-align: center;
  padding: 40px 20px;
  background-color: #F7FAFC;
  border-radius: 8px;
  border: 1px solid #E2E8F0;
}
