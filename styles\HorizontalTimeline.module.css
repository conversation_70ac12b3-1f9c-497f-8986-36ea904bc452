.timelineContainer {
  position: relative;
  width: 100%;
  overflow-x: auto;
  padding: 20px;
}

.timelineLine {
  position: relative;
  height: 6px;
  background-color: #E2E8F0;
  margin: 80px 0;
  display: flex;
  align-items: center;
}

.timelineSegment {
  flex: 1;
  height: 6px;
  position: relative;
  z-index: 1;
}

.timelinePoint {
  position: relative;
  z-index: 2;
}

.timelineIcon {
  position: absolute;
  top: -27px;
  left: -30px;
  border: 3px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 3;
}

.timelineYear {
  position: absolute;
  font-weight: 700;
  font-size: 1.5rem;
  z-index: 2;
}

.timelineContent {
  position: absolute;
  width: 200px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.startEndCircle {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 3;
  color: white;
  font-weight: 700;
  font-size: 0.8rem;
}


