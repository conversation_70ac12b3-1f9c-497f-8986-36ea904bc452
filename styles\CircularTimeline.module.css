.timelineContainer {
  position: relative;
  width: 100%;
  height: 700px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(to bottom, rgba(245, 247, 250, 0.5), rgba(245, 247, 250, 0));
  border-radius: 16px;
}

.timelineCircle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  animation: rotateCircle 120s linear infinite;
}

@keyframes rotateCircle {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.timelineItem {
  position: absolute;
  transform: translate(-50%, -50%);
  z-index: 2;
  width: 250px;
  transition: transform 0.3s ease-in-out;
}

.timelineItem:hover {
  transform: translate(-50%, -50%) scale(1.05);
}

.timelineItemContent {
  background-color: white;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.timelineItemContent:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.timelineCenter {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
}

.centerContent {
  background: linear-gradient(135deg, #ffffff, #f5f7fa);
  border-radius: 50%;
  width: 150px;
  height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 2px solid #f0f0f0;
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 8px 25px rgba(62, 88, 121, 0.2);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: scale(1);
  }
}

.timelineConnector {
  position: absolute;
  background-color: #e2e8f0;
  z-index: 1;
}

.timelineIcon {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  border: 2px solid white;
}

/* Animation pour les éléments de la timeline */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.timelineItem {
  animation: fadeIn 0.5s ease-out forwards;
  animation-delay: calc(var(--index) * 0.1s);
  opacity: 0;
}
