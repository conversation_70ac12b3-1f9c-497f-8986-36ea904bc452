/* Styles personnalisés pour la vue liste */

/* Style général des cartes de tâches */
.task-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Styles pour les badges */
.badge-status {
  font-weight: 500;
  padding: 4px 8px;
}

.badge-priority {
  font-weight: 500;
  padding: 4px 8px;
}

/* Styles pour les différents statuts */
.status-todo {
  background-color: #f1f3f5;
  color: #495057;
}

.status-in-progress {
  background-color: #e7f5ff;
  color: #1c7ed6;
}

.status-done {
  background-color: #ebfbee;
  color: #37b24d;
}

/* Styles pour les différentes priorités */
.priority-high {
  color: #fa5252;
  border-color: #fa5252;
}

.priority-medium {
  color: #fd7e14;
  border-color: #fd7e14;
}

.priority-low {
  color: #40c057;
  border-color: #40c057;
}

/* Styles pour les tags */
.tag-badge {
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

/* Styles pour les avatars */
.avatar-group {
  display: flex;
}

.avatar-group .avatar {
  margin-right: -8px;
  border: 2px solid white;
}

/* Styles pour les modals */
.task-detail-modal {
  border-radius: 8px;
  overflow: hidden;
}

.task-detail-header {
  padding: 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.task-detail-content {
  padding: 16px;
}

/* Styles pour les filtres */
.filter-badge {
  background-color: #e7f5ff;
  color: #1c7ed6;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Animation pour les cartes de tâches */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.task-card {
  animation: fadeIn 0.3s ease;
}

/* Responsive design */
@media (max-width: 768px) {
  .task-card-content {
    flex-direction: column;
  }
  
  .task-card-actions {
    margin-top: 10px;
    justify-content: flex-start;
  }
}
